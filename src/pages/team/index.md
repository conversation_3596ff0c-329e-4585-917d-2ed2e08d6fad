---
title: Team
description: Built On Open Source, By Open Source, For Open Source.
---

The Apache Cloudberry (Incubating) team works on developing the core database kernel, utilities, tools, as well as the documentation website. 

## Podling PMC
Each Podling Project Management Committee (PPMC) helps its Podling learn how to govern itself. It works like a PMC but reports to the Incubator PMC instead of to the ASF Board. Initially, it is composed of the Podling’s mentors and the initial committers. The PPMC is directly responsible for the oversight of the podling, and it also decides who to add as a PPMC member.

|Name| Email | GitHub ID | Organization|
|--|--|--|--|
| <PERSON><PERSON> | <EMAIL> | x4m |  Yandex Cloud |
| <PERSON> | <EMAIL> | antoniopetrole | Individual |
| Di<PERSON><PERSON> | <EMAIL> | tuhaihe | HashData |
| Ed <PERSON> | <EMAIL> | edespino | Apache Member, Synx Data Labs |
| <PERSON> | <EMAIL> | gspiegelberg | MNTN |
| Hao <PERSON> | <EMAIL> | gfphoenix78 | HashData |
| <PERSON> | <EMAIL> | gaoxueyu | HighGo |
| Jiang<PERSON> Yang | <EMAIL> | yjhjstz | HashData |
| Jiaqi Zhou | <EMAIL> | jiaqizho | Zilliz |
| Jinbao Chen | <EMAIL> | oppenheimer01 | HashData |
| Kirill Reshke | <EMAIL> | reshke  | Yandex Cloud | Yandex Cloud |
| Kent Yao *(Mentor)* | <EMAIL> | yaooqinn | Apache Member, NetEase |
| Louis Mugnano | <EMAIL> | lmugnano4537 | Individual |
| Max Yang | <EMAIL> | my-ship-it | HashData |
| Maxim Smyatkin | <EMAIL> | Smyatkin-Maxim | Yandex Cloud |
| Roman Shaposhnik *(Mentor)* | <EMAIL> | rvs | Ainekko |
| Sen Hu | <EMAIL> | HuSen8891 | HashData |
| Shine Zhang | <EMAIL> | xinzweb | Synx Data Labs |
| Tushar Pednekar | <EMAIL> | 2shar-p | Synx Data Labs |
| Weinan WANG | <EMAIL> | weinan003 | HashData |
| Willem Jiang *(Mentor)*|<EMAIL> | WillemJiang | Apache Member, ByteDance |
| Xiaoran Wang | <EMAIL> | fanfuxiaoran | ByteDance |
| Xin (Alwin) Tang | <EMAIL> | xtangcode | HashData |
| Zhang Mingli | <EMAIL> | avamingli | HashData |

## Committers

Committers are contributors who have shown sustained commitment to the project through ongoing contributions of high quality and have been granted write access to the project repositories.

Committers in Apache Cloudberry (Incubating):
- Have write access to the project's repositories
- Review and merge pull requests from contributors
- Participate in discussions about the project's development
- Help maintain code quality and project standards
- Mentor new contributors and help grow the community

The path to becoming a committer typically involves:

1. **Consistent Contribution**: Make regular, high-quality contributions to the project over time.
2. **Community Involvement**: Participate in discussions, help other users, and engage with the community.
3. **Code Quality**: Demonstrate technical skill and an understanding of the project's architecture.
4. **Review Process**: Help review pull requests from other contributors.
5. **Nomination**: Current committers or PPMC members may nominate you based on your contributions.
6. **Vote**: The PPMC will vote on your nomination. If approved, you'll be invited to become a committer.

Committer status recognizes your contributions and grants you additional responsibilities in shaping the project's future. We encourage all contributors to aim for this role by consistently participating in the project's development.

### New Committers

- [Inviting New Committers](./team/new-committers)
- [Sign the ICLA](./team/sign-icla)
- [Setup the Apache email account](./team/setup-apache-email-account)

The listing includes new committers, excluding PPMC members above:

|Name| Email | GitHub ID | Organization|
|--|--|--|--|
| Xiong Tong | <EMAIL> | TomShawn | HashData |
| Wenchao Zhang | <EMAIL> | zhangwenchao-123 | HashData |
| Xun Gong | <EMAIL> | gongxun0928 | HashData |

## Contributors wall

Our journey began in 2022, and we have built our foundation on PostgreSQL and Greenplum Database. We would not be where we are today without the invaluable contributions of the two project contributors. We extend our heartfelt thanks to them.

import Contributors from '@site/src/components/contributors';

<Contributors />
