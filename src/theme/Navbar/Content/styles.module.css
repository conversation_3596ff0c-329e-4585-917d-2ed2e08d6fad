/*
Hide color mode toggle in small viewports
 */
@media (max-width: 996px) {
  .colorModeToggle {
    display: none;
  }
}

/* Additional responsive styles for navbar spacing */
@media (max-width: 1200px) {
  :global(.navbar__items) {
    gap: 15px;
  }
}

@media (max-width: 1100px) {
  :global(.navbar__items) {
    gap: 10px;
  }
  :global(.navbar__item) {
    margin: 0 5px;
  }
}

@media (max-width: 1050px) {
  :global(.navbar__items) {
    gap: 8px;
  }
  :global(.navbar__item) {
    margin: 0 3px;
  }
}

@media (max-width: 997px) {
  :global(.navbar__items) {
    gap: 5px;
  }
}
