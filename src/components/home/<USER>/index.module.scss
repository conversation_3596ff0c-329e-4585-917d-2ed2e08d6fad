.meetTheCommunity {
  position: relative;
  height: 309px;
  padding-top: 70px;
  margin-bottom: -62px;
  background-color: rgb(17, 15, 15);
  .highTitle {
    position: absolute;
    top: 150px;
    left: 50%;
    text-align: center;
    transform: translateX(-50%);
    font-size: 64px;
    font-weight: 700;
    line-height: 66px;
    background-image: linear-gradient(
      to left,
      #9ee8ee 1%,
      #f78013 55%,
      #9f8ced 97%
    );
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  /* Progressive responsive adjustments for better spacing */
  @media screen and (max-width: 1600px) {
    height: 280px;
    padding-top: 60px;
    .highTitle {
      top: 130px;
      font-size: 60px;
    }
  }

  @media screen and (max-width: 1440px) {
    padding-inline: var(--mobile-padding-width);
    height: 350px;
    padding-top: 50px;
    margin-bottom: -40px;
    .highTitle {
      top: 140px;
      font-size: 56px;
    }
    .contributorList {
      gap: 15px;
      width: auto;
    }
    .bgImg {
      height: 350px;
    }
  }

  @media screen and (max-width: 1200px) {
    height: 320px;
    padding-top: 40px;
    margin-bottom: -30px;
    .highTitle {
      top: 120px;
      font-size: 52px;
    }
  }

  @media screen and (max-width: 1024px) {
    height: 280px;
    padding-top: 30px;
    margin-bottom: -20px;
    .highTitle {
      top: 100px;
      font-size: 48px;
      line-height: 52px;
    }
    .bgImg {
      height: 280px;
      width: 100%;
      background-size: contain;
    }
    .contributorList {
      width: auto;
      grid-template-columns: repeat(12, 1fr);
      gap: 15px;
    }
  }

  @media screen and (max-width: 768px) {
    height: 240px;
    padding-top: 20px;
    margin-bottom: -10px;
    .highTitle {
      top: 80px;
      font-size: 40px;
      line-height: 44px;
    }
  }

  @media screen and (max-width: 480px) {
    height: 200px;
    padding-top: 15px;
    margin-bottom: 0;
    .highTitle {
      top: 60px;
      font-size: 32px;
      line-height: 36px;
    }
  }
}
